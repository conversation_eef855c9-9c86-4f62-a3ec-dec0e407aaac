"""
E-commerce Media Generation and Processing Celery Tasks.

This module contains all tasks related to generating and processing
professional media content for e-commerce stores including images, videos, and text.
Integrates with context engine, prompt engine, and provider system for high-quality results.
"""

import logging
from typing import Dict, Any, Optional, List

from servers.worker.celery_app import celery_app
from servers.worker.base_task import LoggedTask
from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus

logger = logging.getLogger(__name__)




@celery_app.task(name='media.generate_media', bind=True, base=LoggedTask, max_retries=3)
def generate_media(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate professional e-commerce media using comprehensive AI pipeline.

    This task handles the complete media generation workflow including:
    - Product context analysis and enrichment
    - Professional prompt generation for different media types
    - Multi-provider generation with fallback support
    - Quality assessment and optimization
    - Storage and variant management

    Args:
        task_data: Complete task data including full payload

    Returns:
        Generation results with comprehensive metadata
    """
    from core.db.database import SessionLocal
    from sqlalchemy import select, update
    from modules.media.service import media_service
    from modules.media.engines.context_engine import context_engine
    from modules.media.schemas import (
        ProviderMediaRequest,
        ProductContext,
        ShopBranding,
        ProductCategory,
        TargetAudience,
        ContentStyle,
        UsageContext
    )
    from modules.storage.storage_service import media_storage_service
    from datetime import datetime
    import asyncio

    db = SessionLocal()

    try:
        job_id = task_data.get("job_id")
        user_id = task_data.get("user_id")
        full_payload = task_data.get("full_payload")

        if not job_id:
            raise ValueError("Job ID is required")

        try:
            # Get the job from database
            job_result = db.execute(select(MediaJob).filter(MediaJob.id == job_id))
            job = job_result.scalar_one_or_none()

            if not job:
                raise ValueError(f"Job {job_id} not found")

            # Update job status to processing
            job.status = MediaJobStatus.PROCESSING
            job.started_at = datetime.now()
            db.commit()
        except ValueError as e:
            logger.error(f"Media generation failed for job {job_id}: {e}")
            # Mark the job as failed and do not retry
            if job: # If job object exists, update its status
                job.status = MediaJobStatus.FAILED
                job.ended_at = datetime.now()
                db.commit()
            return # Exit the task, do not retry

        logger.info(f"Starting media generation for job {job_id}")

        # Extract generation parameters from full payload
        media_type = full_payload.get("media_type", full_payload.get("mode", "image"))
        model = full_payload.get("model", "banana")
        settings = full_payload.get("settings", {})
        items = full_payload.get("items", [])

        # Generate idempotency key if not provided
        idempotency_key = full_payload.get("idempotency_key")
        if not idempotency_key:
            from modules.media.schemas import MediaGenerateRequest
            temp_request = MediaGenerateRequest(**full_payload)
            idempotency_key = temp_request.generate_idempotency_key()

        # Check for existing results with same idempotency key
        if not full_payload.get("force_regenerate", False):
            cached_result = asyncio.run(_check_cached_result(idempotency_key, user_id))
            if cached_result:
                logger.info(f"Returning cached result for idempotency key {idempotency_key}")
                return cached_result

        # Check quota and budget before generation
        quota_check = asyncio.run(_check_user_quota_and_budget(user_id, media_type, len(items)))
        if not quota_check["allowed"]:
            logger.warning(f"Quota/budget exceeded for user {user_id}: {quota_check['reason']}")
            return {
                'status': 'quota_exceeded',
                'message': quota_check['reason'],
                'quota_info': quota_check.get('details', {})
            }

        # Process each product item with comprehensive context
        product_id = None # Initialize product_id for error logging
        results = []
        for i, item in enumerate(items):
            try:
                # Extract product information
                product_id = str(item.get("productId", item.get("product_id", "")) or "")
                if not product_id:
                    product_id = f"product_{i+1}"  # Fallback for missing product ID

                product_title = item.get("title", item.get("prompt", "")) or product_id

                logger.info(f"Processing product {product_id}: {product_title}")

                # Create comprehensive product context
                product_context = asyncio.run(_create_enhanced_product_context(item, full_payload))

                # Create shop branding context
                shop_branding = asyncio.run(_create_shop_branding_context(full_payload))

                # Create media generation request
                media_request = ProviderMediaRequest(
                    product_title=product_title,
                    media_type=media_type,
                    product_context=product_context,
                    shop_branding=shop_branding,
                    custom_prompt=item.get("prompt"),
                    num_images=4 if media_type == "image" else 1,
                    variants_count=4,
                    aspect_ratio=settings.get("aspectRatio", "1:1"),
                    style="professional",
                    model=model,
                    settings=settings,
                    custom_config={
                        "mode": full_payload.get("mode"),
                        "settings": settings,
                        "reference_images": item.get("referenceImageUrls", []),
                        "guidance": settings.get("guidance", 7.5),
                        "steps": settings.get("steps", 25),
                        "seed": settings.get("seed"),
                        "upscale": settings.get("upscale", True),
                        "safety": settings.get("safety", True)
                    },
                    target_platforms=full_payload.get("target_platforms", ["website"]),
                    campaign_theme=full_payload.get("campaign_theme"),
                    call_to_action=full_payload.get("call_to_action")
                )

                # Determine the actual provider to use, considering overrides
                provider_name = media_service._get_provider_for_media_type(media_type, model)

                # Generate media using enhanced provider system
                result = asyncio.run(_run_async_generation(media_service, provider_name, media_request))

                if result is None:
                    logger.error(f"Media generation failed for product {product_id}. _run_async_generation returned None.")
                    asyncio.run(_update_job_variants(db, job.id, {"error": "Media generation failed"}, MediaVariantStatus.FAILED))
                    results.append({
                        "product_id": product_id,
                        "status": "failed",
                        "error": "Media generation failed"
                    })
                    continue # Skip to the next product

                if result.success:
                    # Apply content safety checks
                    safety_result = asyncio.run(_apply_content_safety_checks(result, media_request, product_context))

                    # Process and store generated media
                    processed_result = asyncio.run(_process_generation_result(
                        result, product_id, user_id, media_type
                    ))

                    if processed_result is None:
                        logger.error(f"Media generation failed for product {product_id}. _process_generation_result returned None.")
                        asyncio.run(_update_job_variants(db, job.id, {"error": "Media generation failed"}, MediaVariantStatus.FAILED))
                        results.append({
                            "product_id": product_id,
                            "status": "failed",
                            "error": "Media generation failed"
                        })
                        continue # Skip to the next product

                    # Add safety information to processed result
                    processed_result["safety_checked"] = True
                    processed_result["content_flags"] = safety_result.get("flags", [])
                    processed_result["needs_review"] = not safety_result.get("safe", True)

                    # Update variants with results
                    variant_status = MediaVariantStatus.READY if safety_result.get("safe", True) else MediaVariantStatus.PROCESSING
                    asyncio.run(_update_job_variants(db, job.id, processed_result, variant_status))

                    # Deduct quota and budget after successful generation
                    asyncio.run(_deduct_user_quota_and_budget(user_id, media_type, 1))

                    results.append({
                        "product_id": product_id,
                        "status": "success",
                        "media_urls": processed_result.get("media_urls", []),
                        "metadata": processed_result.get("metadata", {}),
                        "quality_score": processed_result.get("quality_score", 0.8),
                        "safety_checked": processed_result.get("safety_checked", False),
                        "content_flags": processed_result.get("content_flags", [])
                    })
                else:
                    # Mark variants as failed
                    asyncio.run(_update_job_variants(db, job.id, {"error": result.error_message}, MediaVariantStatus.FAILED))

                    results.append({
                        "product_id": product_id,
                        "status": "failed",
                        "error": result.error_message
                    })

            except Exception as e:
                logger.error(f"Error processing product {product_id}: {e}")
                results.append({
                    "product_id": product_id,
                    "status": "failed",
                    "error": str(e)
                })

        # Determine final job status based on results
        successful_products = len([r for r in results if r["status"] == "success"])
        total_products = len(items)

        if successful_products == 0:
            # All products failed - job should be failed
            job.status = MediaJobStatus.FAILED
            job.error_message = "All products failed to generate"
            final_status = "failed"
        elif successful_products < total_products:
            # Some products failed - job completed with partial success
            job.status = MediaJobStatus.COMPLETED
            job.error_message = f"{total_products - successful_products} out of {total_products} products failed"
            final_status = "completed_with_errors"
        else:
            # All products succeeded
            job.status = MediaJobStatus.COMPLETED
            final_status = "completed"

        job.completed_at = datetime.now()
        db.commit()

        logger.info(f"Media generation {final_status} for job {job_id}: {successful_products}/{total_products} products succeeded")
        return {
            "job_id": job_id,
            "status": final_status,
            "results": results,
            "total_products": total_products,
            "successful_products": successful_products,
            "generation_metadata": {
                "media_type": media_type,
                "model_used": model,
                "enhanced_pipeline": True,
                "context_enriched": True
            }
        }

    except Exception as e:
        logger.error(f"Media generation failed for job {task_data.get('job_id')}: {e}", exc_info=True)

        # Update job status to failed
        try:
            if 'job' in locals() and job:
                job.status = MediaJobStatus.FAILED
                job.error_message = str(e)
                job.completed_at = datetime.now()
                db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update job status: {db_error}")

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries * 60  # 1min, 2min, 4min
            logger.info(f"Retrying media generation for job {task_data.get('job_id')} in {countdown}s")
            raise self.retry(countdown=countdown, exc=e)

        raise

    finally:
        db.close()


@celery_app.task(name='media.cleanup_old_media', base=LoggedTask)
def cleanup_old_media(days_old: int = 90):
    """
    Clean up old generated media files to save storage space.

    Args:
        days_old: Number of days old media to keep
    """
    from core.db.database import SessionLocal
    from datetime import datetime, timezone, timedelta
    import os

    db = SessionLocal()

    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)

        # Query old media records
        from sqlalchemy import select, and_
        from modules.media.models import MediaJob, MediaVariant
        from modules.storage.storage_service import media_storage_service

        # Find old completed jobs
        old_jobs = db.execute(
            select(MediaJob).filter(
                and_(
                    MediaJob.completed_at < cutoff_date,
                    MediaJob.status == MediaJobStatus.COMPLETED
                )
            )
        ).scalars().all()

        cleaned_files = 0
        cleaned_jobs = 0

        for job in old_jobs:
            try:
                # Get variants for this job
                variants = db.execute(
                    select(MediaVariant).filter(MediaVariant.job_id == job.id)
                ).scalars().all()

                # Delete media files from storage
                for variant in variants:
                    try:
                        if variant.image_url and "storage" in variant.image_url:
                            # Extract storage path and delete
                            storage_path = variant.image_url.split("/storage/")[-1]
                            asyncio.run(media_storage_service.delete_media(storage_path))
                            cleaned_files += 1

                        if variant.video_url and "storage" in variant.video_url:
                            storage_path = variant.video_url.split("/storage/")[-1]
                            asyncio.run(media_storage_service.delete_media(storage_path))
                            cleaned_files += 1

                    except Exception as e:
                        logger.warning(f"Failed to delete media file for variant {variant.id}: {e}")

                # Delete database records
                for variant in variants:
                    db.delete(variant)

                db.delete(job)
                cleaned_jobs += 1

            except Exception as e:
                logger.error(f"Failed to cleanup job {job.id}: {e}")

        db.commit()

        logger.info(f"Cleaned up {cleaned_jobs} jobs and {cleaned_files} media files older than {cutoff_date}")

        return {
            'status': 'completed',
            'message': f'Cleaned up {cleaned_jobs} jobs and {cleaned_files} files',
            'cleaned_jobs': cleaned_jobs,
            'cleaned_files': cleaned_files
        }

    except Exception as e:
        logger.error(f"Error cleaning up old media: {e}", exc_info=True)
        raise
    finally:
        db.close()


async def _check_cached_result(idempotency_key: str, user_id: int) -> Optional[Dict[str, Any]]:
    """Check for cached media generation result."""
    try:
        from core.db.database import SessionLocal
        from sqlalchemy import select, and_
        from modules.media.models import MediaJob, MediaVariant

        db = SessionLocal()

        # Look for completed job with same idempotency key
        job = db.execute(
            select(MediaJob).filter(
                and_(
                    MediaJob.idempotency_key == idempotency_key,
                    MediaJob.user_id == user_id,
                    MediaJob.status == MediaJobStatus.COMPLETED
                )
            )
        ).scalar_one_or_none()

        if job:
            # Get variants for this job
            variants = db.execute(
                select(MediaVariant).filter(MediaVariant.job_id == job.id)
            ).scalars().all()

            # Return cached result
            result = {
                'success': True,
                'job_id': job.id,
                'cached_result': True,
                'idempotency_key': idempotency_key,
                'variants': [
                    {
                        'id': v.id,
                        'image_url': v.image_url,
                        'video_url': v.video_url,
                        'text_content': v.text_content,
                        'status': v.status.value,
                        'quality_score': v.quality_score
                    }
                    for v in variants
                ]
            }

            db.close()
            return result

        db.close()
        return None

    except Exception as e:
        logger.warning(f"Failed to check cached result: {e}")
        return None


def _generate_product_version_hash(product_data: Dict[str, Any]) -> str:
    """Generate version hash for product data to detect changes."""
    import hashlib
    import json

    # Include only fields that affect media generation
    version_data = {
        "title": product_data.get("title"),
        "description": product_data.get("description"),
        "category": product_data.get("category"),
        "colors": product_data.get("colors"),
        "materials": product_data.get("materials"),
        "key_features": product_data.get("key_features"),
        "target_audience": product_data.get("target_audience")
    }

    version_string = json.dumps(version_data, sort_keys=True)
    return hashlib.sha256(version_string.encode()).hexdigest()[:12]


def _should_regenerate_media(
    existing_version: Optional[str],
    current_version: str,
    media_type: str,
    changed_fields: List[str]
) -> bool:
    """Determine if media should be regenerated based on product changes."""
    if not existing_version or existing_version != current_version:
        # Check if changes affect this media type
        if media_type == "text":
            # Text regeneration needed for description, features, or category changes
            text_affecting_fields = {"title", "description", "key_features", "category", "target_audience"}
            return bool(set(changed_fields) & text_affecting_fields)
        elif media_type == "image":
            # Image regeneration needed for visual changes
            image_affecting_fields = {"title", "colors", "materials", "category"}
            return bool(set(changed_fields) & image_affecting_fields)
        elif media_type == "video":
            # Video regeneration needed for most changes
            video_affecting_fields = {"title", "description", "colors", "materials", "category", "key_features"}
            return bool(set(changed_fields) & video_affecting_fields)

    return False


async def _handle_multi_variant_products(items: List[Dict[str, Any]], full_payload: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Handle multi-variant products by deciding whether to generate universal or variant-specific assets."""
    if len(items) <= 1:
        return items

    # Check if items are variants of the same product
    base_product_ids = set()
    for item in items:
        product_id = str(item.get("product_id", ""))
        # Extract base product ID (remove variant suffix like -red, -large, etc.)
        base_id = product_id.split("-")[0] if "-" in product_id else product_id
        base_product_ids.add(base_id)

    if len(base_product_ids) == 1:
        # Multi-variant product - create universal asset
        logger.info(f"Detected multi-variant product with {len(items)} variants")

        # Merge variant information into a single universal item
        universal_item = items[0].copy()

        # Combine variant-specific information
        colors = set()
        sizes = set()
        materials = set()

        for item in items:
            if item.get("color"):
                colors.add(item["color"])
            if item.get("size"):
                sizes.add(item["size"])
            if item.get("material"):
                materials.add(item["material"])

        # Update universal item with combined variant info
        if colors:
            universal_item["colors"] = list(colors)
        if sizes:
            universal_item["sizes"] = list(sizes)
        if materials:
            universal_item["materials"] = list(materials)

        # Add note about variants
        universal_item["variant_note"] = f"Available in {len(items)} variants"

        return [universal_item]
    else:
        # Different products - process separately
        return items


async def _generate_alt_text(media_url: str, product_context: 'ProductContext') -> str:
    """Generate alt text for images using AI."""
    try:
        # Basic alt text generation based on product context
        alt_parts = []

        if product_context.title:
            alt_parts.append(product_context.title)

        if product_context.category:
            alt_parts.append(product_context.category.value.replace("_", " "))

        if product_context.colors:
            alt_parts.append(f"in {', '.join(product_context.colors)}")

        if product_context.materials:
            alt_parts.append(f"made of {', '.join(product_context.materials)}")

        alt_text = " ".join(alt_parts)

        # Ensure it's not too long
        if len(alt_text) > 125:
            alt_text = alt_text[:122] + "..."

        return alt_text or "Product image"

    except Exception as e:
        logger.warning(f"Failed to generate alt text: {e}")
        return "Product image"


async def _generate_captions(media_url: str, product_context: 'ProductContext') -> str:
    """Generate captions/subtitles for videos."""
    try:
        # Basic caption generation for product videos
        captions = []

        if product_context.title:
            captions.append(f"Introducing {product_context.title}")

        if product_context.key_features:
            captions.extend([f"Features: {feature}" for feature in product_context.key_features[:3]])

        if product_context.target_audience:
            captions.append(f"Perfect for {product_context.target_audience.value.replace('_', ' ')}")

        captions.append("Shop now!")

        # Format as SRT-style captions
        caption_text = "\n".join([
            f"{i+1}\n00:00:0{i*2},000 --> 00:00:0{(i+1)*2},000\n{caption}\n"
            for i, caption in enumerate(captions[:5])
        ])

        return caption_text

    except Exception as e:
        logger.warning(f"Failed to generate captions: {e}")
        return ""


def _apply_safe_defaults(product_context: Dict[str, Any]) -> Dict[str, Any]:
    """Apply safe defaults for missing product metadata."""
    safe_context = product_context.copy()

    # Safe defaults for missing fields
    if not safe_context.get("title"):
        safe_context["title"] = "Product"

    if not safe_context.get("description"):
        safe_context["description"] = "High-quality product"

    if not safe_context.get("category"):
        safe_context["category"] = "general"

    if not safe_context.get("colors"):
        # Don't specify colors if unknown
        pass

    if not safe_context.get("materials"):
        # Don't specify materials if unknown
        pass

    if not safe_context.get("target_audience"):
        safe_context["target_audience"] = "general_consumers"

    if not safe_context.get("key_features"):
        safe_context["key_features"] = ["High quality", "Durable", "Stylish"]

    return safe_context


async def _apply_content_safety_checks(
    result: 'ProviderMediaResult',
    request: 'ProviderMediaRequest',
    product_context: Optional['ProductContext'] = None
) -> Dict[str, Any]:
    """Apply content safety and legal compliance checks to generated content."""
    try:
        from modules.compliance.content_safety import content_safety_service

        safety_results = {
            "safe": True,
            "flags": [],
            "details": {},
            "filtered_content": {}
        }

        # Check text content if available
        if hasattr(result, 'variants') and result.variants:
            for i, variant in enumerate(result.variants):
                variant_flags = []
                variant_details = {}

                # Check text content
                if variant.get("text_content"):
                    text_safe, text_flags, text_details = await content_safety_service.check_content_safety(
                        variant["text_content"],
                        content_type="text",
                        product_category=product_context.category.value if product_context and product_context.category else None
                    )

                    if not text_safe:
                        variant_flags.extend(text_flags)
                        variant_details.update(text_details)

                        # Filter the content
                        filtered_text = content_safety_service.filter_content(
                            variant["text_content"],
                            text_flags
                        )
                        safety_results["filtered_content"][f"variant_{i}_text"] = filtered_text

                # Check image content
                if variant.get("image_url"):
                    image_safe, image_flags, image_details = await content_safety_service.check_content_safety(
                        variant["image_url"],
                        content_type="image",
                        product_category=product_context.category.value if product_context and product_context.category else None
                    )

                    if not image_safe:
                        variant_flags.extend(image_flags)
                        variant_details.update(image_details)

                # Check video content
                if variant.get("video_url"):
                    # Validate rights for video content
                    rights_valid, rights_issues = await content_safety_service.validate_rights(
                        "video",
                        media_url=variant["video_url"],
                        music_used=variant.get("music_used"),
                        audio_used=variant.get("audio_used")
                    )

                    if not rights_valid:
                        variant_flags.append("rights_issues")
                        variant_details["rights_issues"] = rights_issues

                if variant_flags:
                    safety_results["safe"] = False
                    safety_results["flags"].extend(variant_flags)
                    safety_results["details"][f"variant_{i}"] = variant_details

        # Check the main prompt for safety
        if hasattr(request, 'custom_prompt') and request.custom_prompt:
            prompt_safe, prompt_flags, prompt_details = await content_safety_service.check_content_safety(
                request.custom_prompt,
                content_type="text",
                product_category=product_context.category.value if product_context and product_context.category else None
            )

            if not prompt_safe:
                safety_results["safe"] = False
                safety_results["flags"].extend(prompt_flags)
                safety_results["details"]["prompt"] = prompt_details

        return safety_results

    except Exception as e:
        logger.warning(f"Content safety check failed: {e}")
        return {
            "safe": True,  # Default to safe if check fails
            "flags": [],
            "details": {"error": str(e)},
            "filtered_content": {}
        }


# Helper functions for media generation

async def _create_enhanced_product_context(item: Dict[str, Any], full_payload: Dict[str, Any]) -> 'ProductContext':
    """Create comprehensive product context from item data."""
    from modules.media.schemas import ProductContext, ProductCategory, TargetAudience

    # Extract product information
    title = item.get("title", item.get("prompt", "")) or "Product"
    description = item.get("description", "")

    # Infer category from title/description
    category = _infer_product_category(title, description)

    # Extract attributes
    colors = _extract_colors_from_text(f"{title} {description}")
    materials = _extract_materials_from_text(f"{title} {description}")

    # Determine target audience
    target_audience = _infer_target_audience(title, description, full_payload)

    return ProductContext(
        title=title,
        description=description,
        category=category,
        colors=colors,
        materials=materials,
        target_audience=target_audience,
        key_features=_extract_key_features(description),
        shopify_product_id=str(item.get("productId", "")),
        shopify_tags=item.get("tags", []),
        existing_images=item.get("referenceImageUrls", [])
    )


async def _create_shop_branding_context(full_payload: Dict[str, Any]) -> Optional['ShopBranding']:
    """Create shop branding context from payload."""
    from modules.media.schemas import ShopBranding, ContentStyle

    shop_info = full_payload.get("shop_info", {})
    if not shop_info:
        return None

    return ShopBranding(
        shop_name=shop_info.get("name", "Store"),
        brand_voice=shop_info.get("brand_voice", "professional"),
        color_palette=shop_info.get("colors", []),
        visual_style=ContentStyle(shop_info.get("visual_style", "professional")),
        brand_values=shop_info.get("values", [])
    )


async def _run_async_generation(media_service, model: str, request: 'ProviderMediaRequest'):
    """Run async media generation in sync context."""
    import asyncio

    # Create new event loop if none exists
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    return await media_service.generate_media_with_provider(model, request)


async def _process_generation_result(
    result,
    product_id: str,
    user_id: int,
    media_type: str
) -> Dict[str, Any]:
    """Process and store generated media."""
    from modules.storage.storage_service import media_storage_service

    processed_result = {
        "media_urls": [],
        "metadata": {},
        "quality_score": 0.8
    }

    try:
        if media_type == "image" and result.images:
            for image in result.images:
                # For now, just collect URLs - in production, download and store
                processed_result["media_urls"].append(image.get("image_url"))

        elif media_type == "video" and result.variants:
            for variant in result.variants:
                processed_result["media_urls"].append(variant.get("video_url"))

        elif media_type == "text" and result.variants:
            # Store text content
            processed_result["text_content"] = result.variants

        processed_result["metadata"] = {
            "provider_job_id": result.provider_job_id,
            "generation_time": result.estimated_completion_time,
            "provider_used": "enhanced_pipeline"
        }

    except Exception as e:
        logger.error(f"Error processing generation result: {e}")

    return processed_result


async def _update_job_variants(db, job_id: int, result_data: Dict[str, Any], status):
    """Update job variants with generation results."""
    from sqlalchemy import select
    from modules.media.models import MediaVariant
    from datetime import datetime

    variants = db.execute(
        select(MediaVariant).filter(MediaVariant.job_id == job_id)
    ).scalars().all()

    media_urls = result_data.get("media_urls", [])

    for i, variant in enumerate(variants):
        variant.status = status
        variant.updated_at = datetime.now()

        if status.name == "READY" and i < len(media_urls):
            if "image" in media_urls[i]:
                variant.image_url = media_urls[i]
            elif "video" in media_urls[i]:
                variant.video_url = media_urls[i]
        elif status.name == "FAILED":
            variant.error_message = result_data.get("error", "Generation failed")


def _infer_product_category(title: str, description: str) -> 'ProductCategory':
    """Infer product category from title and description."""
    from modules.media.schemas import ProductCategory

    text = f"{title} {description}".lower()

    if any(keyword in text for keyword in ["shoe", "boot", "sneaker", "sandal"]):
        return ProductCategory.FOOTWEAR
    elif any(keyword in text for keyword in ["shirt", "dress", "pants", "jacket"]):
        return ProductCategory.FASHION_APPAREL
    elif any(keyword in text for keyword in ["bag", "wallet", "watch", "sunglasses"]):
        return ProductCategory.ACCESSORIES
    elif any(keyword in text for keyword in ["makeup", "skincare", "beauty"]):
        return ProductCategory.BEAUTY_COSMETICS
    elif any(keyword in text for keyword in ["necklace", "ring", "earring", "jewelry"]):
        return ProductCategory.JEWELRY
    else:
        return ProductCategory.FASHION_APPAREL


def _extract_colors_from_text(text: str) -> List[str]:
    """Extract color information from text."""
    colors = []
    color_keywords = [
        "black", "white", "red", "blue", "green", "yellow", "orange",
        "purple", "pink", "brown", "gray", "grey", "navy", "beige"
    ]

    text_lower = text.lower()
    for color in color_keywords:
        if color in text_lower:
            colors.append(color)

    return colors


def _extract_materials_from_text(text: str) -> List[str]:
    """Extract material information from text."""
    materials = []
    material_keywords = [
        "cotton", "silk", "wool", "leather", "denim", "polyester",
        "metal", "gold", "silver", "plastic", "wood"
    ]

    text_lower = text.lower()
    for material in material_keywords:
        if material in text_lower:
            materials.append(material)

    return materials


def _extract_key_features(description: str) -> List[str]:
    """Extract key features from product description."""
    features = []
    feature_keywords = [
        "waterproof", "breathable", "comfortable", "durable", "lightweight",
        "wireless", "rechargeable", "adjustable", "machine washable"
    ]

    description_lower = description.lower()
    for feature in feature_keywords:
        if feature in description_lower:
            features.append(feature)

    return features


def _infer_target_audience(title: str, description: str, payload: Dict[str, Any]) -> List['TargetAudience']:
    """Infer target audience from product information."""
    from modules.media.schemas import TargetAudience

    audiences = []
    text = f"{title} {description}".lower()

    if any(keyword in text for keyword in ["trendy", "young", "teen"]):
        audiences.append(TargetAudience.GEN_Z)
    elif any(keyword in text for keyword in ["professional", "business", "office"]):
        audiences.append(TargetAudience.PROFESSIONALS)
    elif any(keyword in text for keyword in ["luxury", "premium", "high-end"]):
        audiences.append(TargetAudience.LUXURY_BUYERS)
    else:
        audiences.append(TargetAudience.MILLENNIALS)

    return audiences


async def _check_user_quota_and_budget(user_id: int, media_type: str, quantity: int) -> Dict[str, Any]:
    """Check if user has quota and budget for generation."""
    try:
        from modules.billing.quota_service import quota_service

        # Check quota
        has_quota, quota_info = await quota_service.check_quota(user_id, media_type, quantity)
        if not has_quota:
            return {
                "allowed": False,
                "reason": f"Quota exceeded: {quota_info.get('quota_type', 'unknown')} limit reached",
                "details": quota_info
            }

        # Check budget
        has_budget, budget_info = await quota_service.check_budget(user_id, media_type, quantity)
        if not has_budget:
            return {
                "allowed": False,
                "reason": f"Budget exceeded: {budget_info.get('budget_type', 'insufficient funds')}",
                "details": budget_info
            }

        return {
            "allowed": True,
            "quota_info": quota_info,
            "budget_info": budget_info
        }

    except Exception as e:
        logger.error(f"Quota/budget check failed: {e}")
        return {
            "allowed": False,
            "reason": f"Quota check failed: {str(e)}",
            "details": {}
        }


async def _deduct_user_quota_and_budget(user_id: int, media_type: str, quantity: int) -> bool:
    """Deduct quota and budget after successful generation."""
    try:
        from modules.billing.quota_service import quota_service

        success = await quota_service.deduct_quota_and_budget(user_id, media_type, quantity)
        if success:
            logger.info(f"Successfully deducted quota/budget for user {user_id}: {quantity} {media_type}")
        else:
            logger.warning(f"Failed to deduct quota/budget for user {user_id}")

        return success

    except Exception as e:
        logger.error(f"Quota/budget deduction failed: {e}")
        return False




