"""
Provider Manager for Media Generation Providers.
Handles explicit loading and management of media providers.
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Type

from .base import (
    BaseMediaProvider,
    ProviderConfig
)

# Explicit imports of all provider classes
from .image.banana import BananaProvider
from .image.example_image import ExampleImageProvider
from .video.veo3 import Veo3Provider
from .video.example_video import ExampleVideoProvider
from .text.gemini import GeminiProvider
from .text.example_text import ExampleTextProvider

logger = logging.getLogger(__name__)


class MediaProviderRegistry:
    """Registry for managing media provider plugins."""

    def __init__(self):
        self._providers: Dict[str, BaseMediaProvider] = {}
        self._configs: Dict[str, ProviderConfig] = {}

    def register_provider(self, provider: BaseMediaProvider, config: ProviderConfig):
        """Register a provider plugin."""
        self._providers[provider.provider_name] = provider
        self._configs[provider.provider_name] = config

    def unregister_provider(self, provider_name: str):
        """Unregister a provider plugin."""
        if provider_name in self._providers:
            del self._providers[provider_name]
            del self._configs[provider_name]

    def get_provider(self, provider_name: str) -> Optional[BaseMediaProvider]:
        """Get a registered provider."""
        provider = self._providers.get(provider_name)
        if not provider:
            available_providers = list(self._providers.keys())
            if not available_providers:
                raise RuntimeError("No media providers are available. Provider registry is empty. Please check provider configuration and initialization.")
            raise ValueError(f"Provider '{provider_name}' is not registered. Available providers: {available_providers}")

        return provider

    def get_available_providers(self, media_type: Optional[str] = None) -> List[str]:
        """Get list of available providers, optionally filtered by media type."""
        if media_type:
            return [
                name for name, provider in self._providers.items()
                if media_type in provider.supported_media_types
            ]
        return list(self._providers.keys())

    def get_provider_config(self, provider_name: str) -> Optional[ProviderConfig]:
        """Get configuration for a provider."""
        return self._configs.get(provider_name)

    async def initialize_all_providers(self) -> Dict[str, bool]:
        """Initialize all registered providers."""
        results = {}
        for name, provider in self._providers.items():
            config = self._configs.get(name)
            if config:
                results[name] = await provider.initialize(config)
            else:
                results[name] = False
        return results

    async def cleanup_all_providers(self) -> None:
        """Cleanup all registered providers."""
        for provider in self._providers.values():
            await provider.cleanup()


class MediaProviderManager:
    """Manager for explicitly loading and managing media providers."""

    def __init__(self):
        # Explicit list of all available provider classes
        # When adding a new provider, add it to this list
        self._provider_classes: Dict[str, Type[BaseMediaProvider]] = {
            "banana": BananaProvider,
            "example_image": ExampleImageProvider,
            "veo3": Veo3Provider,
            "example_video": ExampleVideoProvider,
            "gemini": GeminiProvider,
            "example_text": ExampleTextProvider,
        }

        logger.info(f"ProviderManager initialized with {len(self._provider_classes)} explicit providers: {list(self._provider_classes.keys())}")

    def get_provider_class(self, provider_name: str) -> Optional[Type[BaseMediaProvider]]:
        """Get a provider class by name."""
        return self._provider_classes.get(provider_name)

    def get_available_providers(self) -> List[str]:
        """Get list of all available provider names."""
        return list(self._provider_classes.keys())

    async def create_provider_instance(
        self,
        provider_name: str,
        config: ProviderConfig
    ) -> Optional[BaseMediaProvider]:
        """Create an instance of a media provider."""
        logger.info(f"Creating provider instance for: {provider_name}")

        provider_class = self.get_provider_class(provider_name)
        if not provider_class:
            logger.error(f"Provider class not found for: {provider_name}")
            logger.error(f"Available providers: {self.get_available_providers()}")
            raise ValueError(f"Provider class not found for: {provider_name}")

        logger.info(f"Found provider class: {provider_class}")

        try:
            logger.info(f"Instantiating provider class: {provider_class}")
            provider_instance = provider_class()
            logger.info(f"Created provider instance: {provider_instance}")
            logger.info(f"Provider name from instance: {provider_instance.provider_name}")

            logger.info(f"Initializing provider with config...")
            success = await provider_instance.initialize(config)

            if success:
                logger.info(f"Provider initialization successful, registering...")
                provider_registry.register_provider(provider_instance, config)
                logger.info(f"Registered provider instance: {provider_name}")
                return provider_instance
            else:
                logger.error(f"Failed to initialize provider: {provider_name}")
                return None

        except Exception as e:
            logger.error(f"Error creating provider instance {provider_name}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None


# Load configuration from JSON file
config_path = Path(__file__).parent / "configs" / "providers_config.json"
with open(config_path, 'r') as f:
    config = json.load(f)

# Replace environment variable placeholders in API keys
for provider_name, provider_config in config["providers"].items():
    api_key = provider_config.get("api_key", "")
    if api_key.startswith("${") and api_key.endswith("}"):
        env_var = api_key[2:-1]
        actual_key = os.getenv(env_var)
        if actual_key:
            provider_config["api_key"] = actual_key


# Global provider manager instance
provider_manager = MediaProviderManager()

# Global provider registry instance
provider_registry = MediaProviderRegistry()


async def initialize_providers(configs: Dict[str, ProviderConfig]) -> Dict[str, bool]:
    """
    Initialize media providers from configuration using explicit provider classes.

    Args:
        configs: Dictionary mapping provider names to their configurations

    Returns:
        Dictionary with initialization results
    """
    results = {}
    logger.info(f"Starting provider initialization for {len(configs)} providers: {list(configs.keys())}")

    # Get available providers from the explicit list
    available_providers = provider_manager.get_available_providers()
    logger.info(f"Available providers in manager: {available_providers}")

    # Initialize configured providers
    for provider_name, config in configs.items():
        logger.info(f"Initializing provider: {provider_name}")

        if provider_name in available_providers:
            logger.info(f"Creating instance for provider: {provider_name}")
            provider_instance = await provider_manager.create_provider_instance(provider_name, config)
            results[provider_name] = provider_instance is not None
            if provider_instance:
                logger.info(f"Successfully initialized provider: {provider_name}")
            else:
                logger.error(f"Failed to create instance for provider: {provider_name}")
        else:
            logger.warning(f"Provider {provider_name} not found in available providers")
            logger.warning(f"Available providers: {available_providers}")
            results[provider_name] = False

    # Log final registry state
    available_providers_in_registry = provider_registry.get_available_providers()
    logger.info(f"Final registry state - available providers: {available_providers_in_registry}")

    return results