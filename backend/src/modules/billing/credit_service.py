"""
Credit Management Service
Handles credit balance, transactions, and credit-based billing logic.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, desc

from core.db.database import get_async_session_factory
from modules.auth.models import Tenant
from .models import CreditTransaction, CreditTransactionType

logger = logging.getLogger(__name__)


class CreditService:
    """Service for managing tenant credits and transactions."""

    def __init__(self):
        self.default_credit_grants = {
            "free": 0,  # No credits for free tier
            "starter": 1000,  # 1,000 credits per month
            "professional": 10000,  # 10,000 credits per month
            "enterprise": 100000,  # 100,000 credits per month
        }

    async def get_balance(self, tenant_id: int) -> float:
        """Get current credit balance for a tenant."""
        try:
            AsyncSessionLocal = get_async_session_factory()
            db = AsyncSessionLocal()

            # Get tenant
            result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
            tenant = result.scalar_one_or_none()

            if not tenant:
                await db.close()
                return 0.0

            # Clean expired credits first
            await self._clean_expired_credits(db, tenant)

            # Return current balance
            balance = tenant.credits or 0.0
            await db.close()
            return balance

        except Exception as e:
            logger.error(f"Error getting credit balance for tenant {tenant_id}: {e}")
            return 0.0

    async def grant_subscription_credits(self, tenant_id: int, plan_type: str) -> bool:
        """Grant monthly credits based on subscription plan."""
        try:
            AsyncSessionLocal = get_async_session_factory()
            db = AsyncSessionLocal()

            # Get tenant
            result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
            tenant = result.scalar_one_or_none()

            if not tenant:
                await db.close()
                return False

            # Get credit amount for plan
            credit_amount = self.default_credit_grants.get(plan_type, 0)
            if credit_amount == 0:
                await db.close()
                return True  # No credits to grant, but not an error

            # Calculate expiration (end of next month)
            now = datetime.utcnow()
            if now.month == 12:
                expires_at = now.replace(year=now.year + 1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            else:
                expires_at = now.replace(month=now.month + 1, day=1, hour=0, minute=0, second=0, microsecond=0)

            # Add credits to balance
            current_balance = tenant.credits or 0.0
            new_balance = current_balance + credit_amount

            # Update tenant
            tenant.credits = new_balance
            tenant.credit_expires_at = expires_at

            # Create transaction record
            transaction = CreditTransaction(
                tenant_id=tenant_id,
                transaction_type=CreditTransactionType.SUBSCRIPTION_GRANT,
                amount=Decimal(str(credit_amount)),
                balance_after=Decimal(str(new_balance)),
                description=f"Monthly credit grant for {plan_type} plan",
                resource_type="subscription_grant",
                expires_at=expires_at,
                transaction_metadata={"plan_type": plan_type}
            )

            db.add(transaction)
            await db.commit()
            await db.refresh(transaction)

            await db.close()

            logger.info(f"Granted {credit_amount} credits to tenant {tenant_id} for {plan_type} plan")
            return True

        except Exception as e:
            logger.error(f"Error granting subscription credits to tenant {tenant_id}: {e}")
            return False

    async def purchase_credits(self, tenant_id: int, credit_amount: int, stripe_payment_id: str, description: str = None) -> bool:
        """Add credits purchased by tenant."""
        try:
            AsyncSessionLocal = get_async_session_factory()
            db = AsyncSessionLocal()

            # Get tenant
            result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
            tenant = result.scalar_one_or_none()

            if not tenant:
                await db.close()
                return False

            # Calculate expiration (1 year from now for purchased credits)
            expires_at = datetime.utcnow() + timedelta(days=365)

            # Add credits to balance
            current_balance = tenant.credits or 0.0
            new_balance = current_balance + credit_amount

            # Update tenant
            tenant.credits = new_balance
            if not tenant.credit_expires_at or tenant.credit_expires_at < expires_at:
                tenant.credit_expires_at = expires_at

            # Create transaction record
            transaction = CreditTransaction(
                tenant_id=tenant_id,
                transaction_type=CreditTransactionType.PURCHASE,
                amount=Decimal(str(credit_amount)),
                balance_after=Decimal(str(new_balance)),
                description=description or f"Credit purchase: {credit_amount} credits",
                stripe_payment_id=stripe_payment_id,
                resource_type="credit_purchase",
                expires_at=expires_at,
                transaction_metadata={"credit_amount": credit_amount}
            )

            db.add(transaction)
            await db.commit()
            await db.refresh(transaction)

            await db.close()

            logger.info(f"Added {credit_amount} purchased credits to tenant {tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Error purchasing credits for tenant {tenant_id}: {e}")
            return False

    async def deduct_credits(self, tenant_id: int, amount: float, description: str, resource_id: str = None, resource_type: str = None) -> bool:
        """Deduct credits from tenant balance."""
        try:
            AsyncSessionLocal = get_async_session_factory()
            db = AsyncSessionLocal()

            # Get tenant
            result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
            tenant = result.scalar_one_or_none()

            if not tenant:
                await db.close()
                return False

            # Check if sufficient balance
            current_balance = tenant.credits or 0.0
            if current_balance < amount:
                await db.close()
                return False

            # Deduct credits
            new_balance = current_balance - amount
            tenant.credits = new_balance

            # Create transaction record
            transaction = CreditTransaction(
                tenant_id=tenant_id,
                transaction_type=CreditTransactionType.USAGE,
                amount=Decimal(str(-amount)),  # Negative for deduction
                balance_after=Decimal(str(new_balance)),
                description=description,
                resource_id=resource_id,
                resource_type=resource_type,
                transaction_metadata={"deducted_amount": amount}
            )

            db.add(transaction)
            await db.commit()
            await db.refresh(transaction)

            await db.close()

            logger.info(f"Deducted {amount} credits from tenant {tenant_id}, new balance: {new_balance}")
            return True

        except Exception as e:
            logger.error(f"Error deducting credits from tenant {tenant_id}: {e}")
            return False

    async def get_transaction_history(self, tenant_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get credit transaction history for a tenant."""
        try:
            AsyncSessionLocal = get_async_session_factory()
            db = AsyncSessionLocal()

            result = await db.execute(
                select(CreditTransaction)
                .where(CreditTransaction.tenant_id == tenant_id)
                .order_by(desc(CreditTransaction.created_at))
                .limit(limit)
            )

            transactions = result.scalars().all()

            # Convert to dict format
            history = []
            for tx in transactions:
                history.append({
                    "id": tx.id,
                    "type": tx.transaction_type.value,
                    "amount": float(tx.amount),
                    "balance_after": float(tx.balance_after),
                    "description": tx.description,
                    "created_at": tx.created_at.isoformat() if tx.created_at else None,
                    "expires_at": tx.expires_at.isoformat() if tx.expires_at else None,
                    "metadata": tx.transaction_metadata
                })

            await db.close()
            return history

        except Exception as e:
            logger.error(f"Error getting transaction history for tenant {tenant_id}: {e}")
            return []

    async def check_credits_available(self, tenant_id: int, required_amount: float) -> bool:
        """Check if tenant has sufficient credits."""
        balance = await self.get_balance(tenant_id)
        return balance >= required_amount

    async def _clean_expired_credits(self, db: AsyncSession, tenant: Tenant) -> None:
        """Clean up expired credits from tenant balance."""
        try:
            now = datetime.utcnow()

            if tenant.credit_expires_at and tenant.credit_expires_at < now:
                # Find expired transactions
                expired_result = await db.execute(
                    select(CreditTransaction)
                    .where(
                        and_(
                            CreditTransaction.tenant_id == tenant.id,
                            CreditTransaction.expires_at < now,
                            CreditTransaction.transaction_type.in_([
                                CreditTransactionType.SUBSCRIPTION_GRANT,
                                CreditTransactionType.PURCHASE
                            ])
                        )
                    )
                )

                expired_transactions = expired_result.scalars().all()

                # Calculate how many credits to remove
                expired_amount = sum(float(tx.amount) for tx in expired_transactions if tx.amount > 0)

                if expired_amount > 0:
                    # Update balance
                    tenant.credits = max(0, (tenant.credits or 0) - expired_amount)

                    # Create expiration transaction
                    expiration_tx = CreditTransaction(
                        tenant_id=tenant.id,
                        transaction_type=CreditTransactionType.EXPIRATION,
                        amount=Decimal(str(-expired_amount)),
                        balance_after=Decimal(str(tenant.credits)),
                        description=f"Expired credits removed: {expired_amount}",
                        resource_type="credit_expiration",
                        transaction_metadata={"expired_amount": expired_amount}
                    )

                    db.add(expiration_tx)
                    await db.commit()

                    logger.info(f"Cleaned {expired_amount} expired credits from tenant {tenant.id}")

        except Exception as e:
            logger.error(f"Error cleaning expired credits for tenant {tenant.id}: {e}")

    async def renew_subscription_credits(self, tenant_id: int) -> bool:
        """
        Renew monthly subscription credits for a tenant.

        Args:
            tenant_id: Tenant ID

        Returns:
            True if renewal successful
        """
        try:
            AsyncSessionLocal = get_async_session_factory()
            db = AsyncSessionLocal()

            # Get tenant
            result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
            tenant = result.scalar_one_or_none()
            if not tenant:
                await db.close()
                return False

            # Get plan tier and credit amount
            plan_tier = tenant.plan_tier
            monthly_credits = self.default_credit_grants.get(plan_tier, 0)

            if monthly_credits == 0:
                await db.close()
                return True  # No credits to renew for free plan

            # Calculate expiration (end of next month)
            now = datetime.utcnow()
            if now.month == 12:
                next_month = now.replace(year=now.year + 1, month=1, day=1)
            else:
                next_month = now.replace(month=now.month + 1, day=1)

            # Last day of next month
            if next_month.month == 12:
                expires_at = next_month.replace(year=next_month.year + 1, month=1) - timedelta(days=1)
            else:
                expires_at = next_month.replace(month=next_month.month + 1) - timedelta(days=1)

            # Add monthly credits
            current_balance = tenant.credits or 0.0
            new_balance = current_balance + monthly_credits

            tenant.credits = new_balance
            tenant.credit_expires_at = expires_at

            # Create transaction record
            transaction = CreditTransaction(
                tenant_id=tenant.id,
                transaction_type=CreditTransactionType.SUBSCRIPTION_GRANT,
                amount=Decimal(str(monthly_credits)),
                balance_after=Decimal(str(new_balance)),
                description=f"Monthly subscription renewal: {monthly_credits} credits ({plan_tier} plan)",
                resource_type="subscription_renewal",
                expires_at=expires_at,
                transaction_metadata={
                    "renewal_type": "monthly_subscription",
                    "plan_tier": plan_tier,
                    "credit_amount": monthly_credits
                }
            )

            db.add(transaction)
            await db.commit()
            await db.close()

            logger.info(f"Renewed {monthly_credits} credits for tenant {tenant_id} ({plan_tier} plan)")
            return True

        except Exception as e:
            logger.error(f"Failed to renew subscription credits for tenant {tenant_id}: {e}")
            return False

    async def process_monthly_renewals(self) -> int:
        """
        Process monthly credit renewals for all active tenants with subscriptions.

        Returns:
            Number of tenants renewed
        """
        try:
            AsyncSessionLocal = get_async_session_factory()
            db = AsyncSessionLocal()

            # Get all active tenants (excluding free plan)
            result = await db.execute(
                select(Tenant).filter(
                    Tenant.is_active == True,
                    Tenant.plan_tier.in_(['starter', 'professional', 'enterprise'])
                )
            )
            active_tenants = result.scalars().all()

            renewed_count = 0
            for tenant in active_tenants:
                success = await self.renew_subscription_credits(tenant.id)
                if success:
                    renewed_count += 1

            await db.close()

            logger.info(f"Processed monthly renewals for {renewed_count} tenants")
            return renewed_count

        except Exception as e:
            logger.error(f"Failed to process monthly renewals: {e}")
            return 0


# Global instance
credit_service = CreditService()