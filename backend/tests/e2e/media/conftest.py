"""
E2E test configuration for media generation APIs.
This file sets up the test environment with real database, queue, and worker processes.
Only the provider implementations are mocked.
"""

import asyncio
import os
import signal
import subprocess
import time
from pathlib import Path
from typing import AsyncGenerator, Dict, Any, List
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from modules.media.schemas import ProviderMediaResult
from core.db.database import Base


@pytest.fixture(scope="session", autouse=True)
async def setup_test_database():
    """Drop all tables and recreate them for E2E tests."""
    from sqlalchemy.ext.asyncio import create_async_engine
    from core.config import get_settings

    settings = get_settings()
    engine = create_async_engine(settings.DATABASE_URL, echo=False)

    # async with engine.begin() as conn:
    #     await conn.run_sync(Base.metadata.drop_all)
    #     await conn.run_sync(Base.metadata.create_all)

    await engine.dispose()



@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for the test session."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def worker_process():
#     """Start the Celery worker process for E2E testing."""
#     # Ensure we're in testing mode
#     os.environ["TESTING"] = "True"
#     # Set DEBUG logging for detailed test output
#     os.environ["LOG_LEVEL"] = "DEBUG"

#     settings = get_settings()
    
#     # Start worker process
#     worker_cmd = [
#         "uv", "run", "python", "-m", "celery",
#         "-A", "servers.worker.main", "worker",
#         "--loglevel=debug", "--concurrency=2",
#         "-Q", "media-generation,media-push,celery"
#     ]

#     # Change to backend/src directory
#     backend_src_dir = Path(__file__).parent.parent.parent.parent / "src"

#     print(f"Starting Celery worker in {backend_src_dir}")
#     worker_process = subprocess.Popen(
#         worker_cmd,
#         cwd=str(backend_src_dir),
#         env={**os.environ, "PYTHONPATH": str(backend_src_dir)},
#         stdout=None,  # Don't capture stdout so logs show in terminal
#         stderr=None,  # Don't capture stderr so logs show in terminal
#         preexec_fn=os.setsid  # Create new process group
#     )
    
#     # Check if worker is running
#     if worker_process.poll() is not None:
#         raise RuntimeError(f"Worker failed to start (exit code: {worker_process.returncode})")
    
    print("Celery worker started successfully")
    
#     yield worker_process
    
#     # Cleanup: terminate worker
#     print("Terminating Celery worker")
#     try:
#         # Send SIGTERM to the process group
#         os.killpg(os.getpgid(worker_process.pid), signal.SIGTERM)
#         worker_process.wait(timeout=10)
#     except (subprocess.TimeoutExpired, ProcessLookupError):
#         # Force kill if graceful shutdown fails
#         try:
#             os.killpg(os.getpgid(worker_process.pid), signal.SIGKILL)
#         except ProcessLookupError:
#             pass
    
#     print("Celery worker terminated")


@pytest.fixture(scope="session")
async def mock_providers():
    """Mock all media providers to use example providers instead of external APIs."""

    from modules.media.providers.config import ProviderConfig
    from modules.media.providers.manager import provider_manager
    from modules.media.providers.image import ExampleImageProvider
    from modules.media.providers.text import ExampleTextProvider
    from modules.media.providers.video import ExampleVideoProvider
    from modules.media.schemas import ProviderMediaRequest
    import json
    from pathlib import Path

    # Load configurations from the existing providers_config.json
    config_path = Path(__file__).parent.parent.parent.parent / "src" / "modules" / "media" / "providers" / "configs" / "providers_config.json"
    with open(config_path, 'r') as f:
        config_data = json.load(f)

    # Create provider configurations from the JSON file
    image_config = ProviderConfig.from_dict("example_image", config_data["providers"]["example_image"])
    text_config = ProviderConfig.from_dict("example_text", config_data["providers"]["example_text"])
    video_config = ProviderConfig.from_dict("example_video", config_data["providers"]["example_video"])

    # Create provider instances
    image_provider = ExampleImageProvider()
    text_provider = ExampleTextProvider()
    video_provider = ExampleVideoProvider()

    # Initialize providers
    await image_provider.initialize(image_config)
    await text_provider.initialize(text_config)
    await video_provider.initialize(video_config)

    # Mock functions that return real provider results
    async def mock_image_generate(request: ProviderMediaRequest):
        return await image_provider.generate_media(request)

    async def mock_text_generate(request: ProviderMediaRequest):
        return await text_provider.generate_media(request)

    async def mock_video_generate(request: ProviderMediaRequest):
        return await video_provider.generate_media(request)

    # Apply mocks using the new example providers
    with patch("modules.media.providers.image.banana.BananaProvider.generate_media", side_effect=mock_image_generate), \
         patch("modules.media.providers.video.veo3.Veo3Provider.generate_media", side_effect=mock_video_generate), \
         patch("modules.media.providers.text.gemini.GeminiProvider.generate_media", side_effect=mock_text_generate), \
         patch("modules.media.service.media_service.generate_media_with_provider") as mock_service:

        # Mock the service method to use example providers
        async def service_mock(model, request):
            if model == "banana" or request.media_type == "image":
                return await mock_image_generate(request)
            elif model == "veo3" or request.media_type == "video":
                return await mock_video_generate(request)
            elif model == "gemini" or request.media_type == "text":
                return await mock_text_generate(request)
            else:
                return await mock_text_generate(request)  # Default fallback

        mock_service.side_effect = service_mock

        yield {
            "image_provider": image_provider,
            "text_provider": text_provider,
            "video_provider": video_provider,
            "service": service_mock
        }


@pytest_asyncio.fixture
async def e2e_client(
    db_session: AsyncSession,
    authenticated_client: AsyncClient,
    worker_process,
    mock_providers,
    test_tenant  # Ensure tenant with credits exists
) -> AsyncClient:
    """
    E2E test client with worker process running and example providers initialized.
    """
    # Ensure worker is ready
    await asyncio.sleep(1)

    return authenticated_client


@pytest.fixture
def sample_media_requests():
    """Sample media generation requests for testing."""
    return {
        "image_request": {
            "mode": "image",
            "media_type": "image",
            "model": "example_image",  # Explicit provider specification required
            "items": [
                {
                    "product_id": 123,
                    "product_context": {
                        "title": "Premium Wireless Headphones",
                        "description": "High-quality wireless headphones with noise cancellation",
                        "category": "electronics",
                        "price": 199.99,
                        "currency": "USD",
                        "colors": ["black", "white"],
                        "key_features": ["wireless", "noise cancellation", "long battery life"]
                    }
                }
            ],
            "settings": {
                "aspect_ratio": "1:1",
                "guidance": 7.5,
                "steps": 25,
                "upscale": True
            }
        },
        "video_request": {
            "mode": "video",
            "media_type": "video",
            "model": "example_video",  # Explicit provider specification required
            "items": [
                {
                    "product_id": 456,
                    "product_context": {
                        "title": "Running Shoes",
                        "description": "Lightweight running shoes for athletes",
                        "category": "footwear",
                        "price": 129.99,
                        "currency": "USD",
                        "colors": ["red", "blue"],
                        "key_features": ["lightweight", "breathable", "durable"]
                    }
                }
            ],
            "settings": {
                "aspect_ratio": "16:9",
                "quality": "high"
            }
        },
        "text_request": {
            "mode": "text",
            "media_type": "text",
            "model": "example_text",  # Explicit provider specification required
            "items": [
                {
                    "product_id": 789,
                    "product_context": {
                        "title": "Leather Handbag",
                        "description": "Elegant leather handbag for professionals",
                        "category": "accessories",
                        "price": 299.99,
                        "currency": "USD",
                        "materials": ["leather"],
                        "target_audience": ["professionals", "luxury_buyers"]
                    }
                }
            ],
            "settings": {
                "style": "professional",
                "tone": "elegant"
            }
        }
    }


@pytest.fixture
def wait_for_job_completion():
    """Helper function to wait for job completion."""
    async def _wait_for_completion(client: AsyncClient, job_id: str, timeout: int = 60):
        """Wait for a job to complete with timeout."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            response = await client.get(f"/api/media/jobs/{job_id}")
            if response.status_code == 200:
                job_data = response.json()
                status = job_data.get("status")
                
                if status in ["completed", "failed"]:
                    return job_data
                    
            await asyncio.sleep(2)  # Check every 2 seconds
        
        raise TimeoutError(f"Job {job_id} did not complete within {timeout} seconds")
    
    return _wait_for_completion


@pytest.fixture
def cleanup_test_jobs():
    """Cleanup test jobs after each test."""
    created_jobs = []
    
    def register_job(job_id):
        created_jobs.append(job_id)
    
    yield register_job
    
    # Cleanup logic would go here if needed
    # For now, we rely on test database isolation
